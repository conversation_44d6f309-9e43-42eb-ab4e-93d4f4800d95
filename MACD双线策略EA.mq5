//+------------------------------------------------------------------+
//|                                                MACD双线策略EA.mq5 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

// 输入参数
input int FastEMA = 12;           // 快线EMA周期
input int SlowEMA = 26;           // 慢线EMA周期
input int MACDEMA = 9;            // MACD信号线EMA周期
input double LotSize = 0.1;       // 手数
input int MagicNumber = 12345;    // 魔术数字
input bool UseTrailingStop = true; // 是否使用移动止损
input int TrailingStopPoints = 200; // 移动止损触发盈利点数
input int MaxConsecutiveBars = 6; // 连续柱数a的最大值限制
input int StopLossBuffer = 50;    // 止损缓冲点数
input int InitialStopLossBuffer = 100; // 初始止损缓冲点数
input bool UseMarginCall = false; // 是否使用亏损补仓功能
input int MaxMarginOrders = 4;    // 最大补仓单数
input int Slippage = 3;           // 滑点
input bool UseMACDTrendFilter = false; // 是否使用MACD快线趋势验证
input bool UseStopLossRecovery = false;  // 是否使用止损触发回本功能
input bool UseRecoveryTakeProfit = true;    // 是否为反向回本单设置止盈
input int RecoveryLookback = 3;    // 反向单止损计算的K线回溯数量
input int RecoveryStopLossBuffer = 144;  // 反向单止损缓冲点数

// 交易类实例
CTrade trade;
CPositionInfo positionInfo;
COrderInfo orderInfo;

// 全局变量 - 用于区分不同图表的EA实例
int UniqueMagicNumber;             // 基于图表的唯一魔术数字
string ChartIdentifier;            // 图表标识符

// 全局变量
double FastEMABuffer[];
double SlowEMABuffer[];
double DIFBuffer[];
double DEABuffer[];
double MACDBuffer[];
bool NewBar = false;

// 补仓相关全局变量
struct MarginCallInfo
{
    ulong MainOrderTicket;        // 主订单号
    double OpenPrice;           // 开仓价
    double StopLoss;           // 止损价
    ENUM_ORDER_TYPE OrderType;             // 订单类型
    bool MarginLevels[20];     // 补仓级别标记（最多支持20级）
    ulong MarginOrderTickets[20]; // 补仓订单号数组
};

MarginCallInfo BuyMarginInfo;   // 多单补仓信息
MarginCallInfo SellMarginInfo;  // 空单补仓信息

// 止损触发回本功能相关全局变量
struct StopLossRecoveryInfo
{
    ulong MainOrderTicket;        // 主订单号
    double OpenPrice;           // 开仓价
    double OriginalStopLoss;    // 原始止损价（首单开仓时的止损）
    ENUM_ORDER_TYPE OrderType;              // 订单类型
    double LotSize;             // 持仓手数
    bool IsActive;              // 是否激活监控
    bool IsRecoveryOrderPlaced; // 是否已下回本单
};

StopLossRecoveryInfo BuyRecoveryInfo;   // 多单回本信息
StopLossRecoveryInfo SellRecoveryInfo;  // 空单回本信息

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 生成基于图表的唯一魔术数字
    ChartIdentifier = _Symbol + "_" + IntegerToString(_Period);
    UniqueMagicNumber = GenerateUniqueMagicNumber();

    // 设置交易类参数
    trade.SetExpertMagicNumber(UniqueMagicNumber);
    trade.SetDeviationInPoints(Slippage);

    // 初始化数组
    ArraySetAsSeries(FastEMABuffer, true);
    ArraySetAsSeries(SlowEMABuffer, true);
    ArraySetAsSeries(DIFBuffer, true);
    ArraySetAsSeries(DEABuffer, true);
    ArraySetAsSeries(MACDBuffer, true);

    int bars = iBars(_Symbol, _Period);
    ArrayResize(FastEMABuffer, bars);
    ArrayResize(SlowEMABuffer, bars);
    ArrayResize(DIFBuffer, bars);
    ArrayResize(DEABuffer, bars);
    ArrayResize(MACDBuffer, bars);

    // 计算历史MACD数据
    CalculateMACD();

    // 参数验证
    if(MaxMarginOrders > 20)
    {
        Print("错误：最大补仓单数不能超过20，当前设置：", MaxMarginOrders);
        MaxMarginOrders = 20;
        Print("已自动调整为：", MaxMarginOrders);
    }

    // 初始化补仓信息
    InitMarginCallInfo();

    // 初始化止损触发回本信息
    InitStopLossRecoveryInfo();

    Print("MACD双线策略EA初始化完成 - 图表：", ChartIdentifier, "，唯一魔术数字：", UniqueMagicNumber);
    Print("MACD快线趋势验证功能：", UseMACDTrendFilter ? "已启用" : "已禁用");
    Print("止损触发回本功能：", UseStopLossRecovery ? "已启用" : "已禁用");
    if(UseStopLossRecovery)
    {
        Print("止损触发回本单止盈功能：", UseRecoveryTakeProfit ? "已启用" : "已禁用");
        Print("回本单止损计算K线回溯数量：", RecoveryLookback);
    }
    if(UseMACDTrendFilter)
    {
        Print("多单条件：MACD柱从负转正 + MACD快线值>0");
        Print("空单条件：MACD柱从正转负 + MACD快线值<0");
    }
    else
    {
        Print("多单条件：MACD柱从负转正");
        Print("空单条件：MACD柱从正转负");
    }
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("MACD双线策略EA已停止 - 图表：", ChartIdentifier);
}

//+------------------------------------------------------------------+
//| 生成基于图表的唯一魔术数字                                        |
//+------------------------------------------------------------------+
int GenerateUniqueMagicNumber()
{
    // 基于货币对和时间周期生成唯一的魔术数字
    string symbolStr = _Symbol;
    int period = _Period;

    // 计算字符串哈希值
    int hash = 0;
    for(int i = 0; i < StringLen(symbolStr); i++)
    {
        hash = hash * 31 + StringGetChar(symbolStr, i);
    }

    // 结合基础魔术数字、哈希值和时间周期
    int uniqueMagic = MagicNumber + (MathAbs(hash) % 10000) + period;

    // 确保魔术数字为正数且不会溢出
    if(uniqueMagic < 0) uniqueMagic = MathAbs(uniqueMagic);

    return uniqueMagic;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查是否有新K线
    static datetime LastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    if(currentBarTime != LastBarTime)
    {
        LastBarTime = currentBarTime;
        NewBar = true;
        
        // 重新计算MACD
        CalculateMACD();
        
        // 检查交易信号
        CheckTradingSignals();
        
        // 移动止损
        if(UseTrailingStop)
            TrailingStop();

        // 补仓检查
        if(UseMarginCall)
            CheckMarginCall();
    }
    else
    {
        NewBar = false;
    }
    
    // 检查止损触发情况（每个tick都检查）
    if(UseStopLossRecovery)
    {
        CheckStopLossTriggered();
    }
}

//+------------------------------------------------------------------+
//| 计算MACD指标                                                      |
//+------------------------------------------------------------------+
void CalculateMACD()
{
    int bars = iBars(_Symbol, _Period);
    
    // 检查并调整数组大小
    if(ArraySize(FastEMABuffer) < bars)
    {
        ArrayResize(FastEMABuffer, bars);
        ArrayResize(SlowEMABuffer, bars);
        ArrayResize(DIFBuffer, bars);
        ArrayResize(DEABuffer, bars);
        ArrayResize(MACDBuffer, bars);
    }

    int limit = MathMin(bars - 1, 1000); // 限制计算范围以提高性能
    
    // 计算快线和慢线EMA
    for(int i = limit; i >= 0; i--)
    {
        // 边界检查
        if(i >= ArraySize(FastEMABuffer) || i >= bars) continue;

        double closePrice = iClose(_Symbol, _Period, i);
        
        if(i == limit)
        {
            FastEMABuffer[i] = closePrice;
            SlowEMABuffer[i] = closePrice;
        }
        else
        {
            // 确保i+1索引有效
            if(i + 1 >= ArraySize(FastEMABuffer)) continue;

            double fastAlpha = 2.0 / (FastEMA + 1);
            double slowAlpha = 2.0 / (SlowEMA + 1);

            FastEMABuffer[i] = fastAlpha * closePrice + (1 - fastAlpha) * FastEMABuffer[i + 1];
            SlowEMABuffer[i] = slowAlpha * closePrice + (1 - slowAlpha) * SlowEMABuffer[i + 1];
        }

        // 计算DIF
        DIFBuffer[i] = FastEMABuffer[i] - SlowEMABuffer[i];
    }
    
    // 计算DEA (信号线)
    for(int i = limit; i >= 0; i--)
    {
        // 边界检查
        if(i >= ArraySize(DEABuffer) || i >= bars) continue;

        if(i == limit)
        {
            DEABuffer[i] = DIFBuffer[i];
        }
        else
        {
            // 确保i+1索引有效
            if(i + 1 >= ArraySize(DEABuffer)) continue;

            double w = 2.0 / (MACDEMA + 1);
            double w1 = 1.0 - w;
            DEABuffer[i] = w * DIFBuffer[i] + w1 * DEABuffer[i + 1];
        }

        // 计算MACD柱
        MACDBuffer[i] = (DIFBuffer[i] - DEABuffer[i]) * 2;
    }
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    if(!NewBar) return;

    int bars = iBars(_Symbol, _Period);
    // 确保有足够的数据
    if(bars < 100) return;

    // 确保数组有足够的数据
    if(ArraySize(MACDBuffer) < 3 || ArraySize(DIFBuffer) < 2) return;

    // 检查多单信号
    if(MACDBuffer[1] > 0 && MACDBuffer[2] < 0)
    {
        // MACD快线趋势验证（如果启用）
        bool trendFilterPassed = true;
        if(UseMACDTrendFilter)
        {
            // 多单需要MACD快线值（DIF）大于0
            if(DIFBuffer[1] <= 0)
            {
                trendFilterPassed = false;
            }
        }

        if(trendFilterPassed)
        {
            // 统计从上上柱开始向前连续小于0的MACD柱值数a
            int a = CountConsecutiveNegative(2);
            if(a > 0 && a < MaxConsecutiveBars) // 添加a值限制条件
            {
                int b = 6 + 2 * a;
                int startBar = b + 2 + a;
                int endBar = 2 + a;

                // 检查指定范围内的MACD柱值是否都大于0
                if(CheckAllPositive(startBar, endBar))
                {
                    // 计算止损价格（最低点减去缓冲点数）
                    double stopLoss = GetLowestPrice(1, endBar) - InitialStopLossBuffer * _Point;
                    OpenBuyOrder(stopLoss);
                }
            }
        }
    }

    // 检查空单信号
    if(MACDBuffer[1] < 0 && MACDBuffer[2] > 0)
    {
        // MACD快线趋势验证（如果启用）
        bool trendFilterPassed = true;
        if(UseMACDTrendFilter)
        {
            // 空单需要MACD快线值（DIF）小于0
            if(DIFBuffer[1] >= 0)
            {
                trendFilterPassed = false;
            }
        }

        if(trendFilterPassed)
        {
            // 统计从上上柱开始向前连续大于0的MACD柱值数a
            int a = CountConsecutivePositive(2);
            if(a > 0 && a < MaxConsecutiveBars) // 添加a值限制条件
            {
                int b = 6 + 2 * a;
                int startBar = b + 2 + a;
                int endBar = 2 + a;

                // 检查指定范围内的MACD柱值是否都小于0
                if(CheckAllNegative(startBar, endBar))
                {
                    // 计算止损价格（最高点加上缓冲点数）
                    double stopLoss = GetHighestPrice(1, endBar) + InitialStopLossBuffer * _Point;
                    OpenSellOrder(stopLoss);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 统计连续负值数量                                                  |
//+------------------------------------------------------------------+
int CountConsecutiveNegative(int startBar)
{
    int count = 0;
    int bars = iBars(_Symbol, _Period);
    for(int i = startBar; i < bars && i < ArraySize(MACDBuffer) && MACDBuffer[i] < 0; i++)
    {
        count++;
    }
    return count;
}

//+------------------------------------------------------------------+
//| 统计连续正值数量                                                  |
//+------------------------------------------------------------------+
int CountConsecutivePositive(int startBar)
{
    int count = 0;
    int bars = iBars(_Symbol, _Period);
    for(int i = startBar; i < bars && i < ArraySize(MACDBuffer) && MACDBuffer[i] > 0; i++)
    {
        count++;
    }
    return count;
}

//+------------------------------------------------------------------+
//| 检查指定范围内是否都为正值                                        |
//+------------------------------------------------------------------+
bool CheckAllPositive(int startBar, int endBar)
{
    int bars = iBars(_Symbol, _Period);
    if(startBar >= bars || endBar < 0 || startBar >= ArraySize(MACDBuffer)) return false;

    for(int i = startBar; i >= endBar; i--)
    {
        if(i >= ArraySize(MACDBuffer) || MACDBuffer[i] <= 0) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 检查指定范围内是否都为负值                                        |
//+------------------------------------------------------------------+
bool CheckAllNegative(int startBar, int endBar)
{
    int bars = iBars(_Symbol, _Period);
    if(startBar >= bars || endBar < 0 || startBar >= ArraySize(MACDBuffer)) return false;

    for(int i = startBar; i >= endBar; i--)
    {
        if(i >= ArraySize(MACDBuffer) || MACDBuffer[i] >= 0) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| 获取指定范围内的最低价                                            |
//+------------------------------------------------------------------+
double GetLowestPrice(int startBar, int endBar)
{
    int bars = iBars(_Symbol, _Period);
    double lowest = iLow(_Symbol, _Period, startBar);
    for(int i = startBar; i <= endBar && i < bars; i++)
    {
        double lowPrice = iLow(_Symbol, _Period, i);
        if(lowPrice < lowest) lowest = lowPrice;
    }
    return lowest;
}

//+------------------------------------------------------------------+
//| 获取指定范围内的最高价                                            |
//+------------------------------------------------------------------+
double GetHighestPrice(int startBar, int endBar)
{
    int bars = iBars(_Symbol, _Period);
    double highest = iHigh(_Symbol, _Period, startBar);
    for(int i = startBar; i <= endBar && i < bars; i++)
    {
        double highPrice = iHigh(_Symbol, _Period, i);
        if(highPrice > highest) highest = highPrice;
    }
    return highest;
}

//+------------------------------------------------------------------+
//| 开多单                                                           |
//+------------------------------------------------------------------+
void OpenBuyOrder(double stopLoss)
{
    // 检查是否已有同方向订单
    if(HasOpenPosition(POSITION_TYPE_BUY)) return;

    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = stopLoss;
    double tp = 0; // 不设置止盈

    if(trade.Buy(LotSize, _Symbol, price, sl, tp, "MACD双线策略-多单[" + ChartIdentifier + "]"))
    {
        ulong ticket = trade.ResultOrder();
        string trendInfo = "";
        if(UseMACDTrendFilter)
        {
            trendInfo = "，MACD快线值：" + DoubleToString(DIFBuffer[1], 5);
        }
        Print("多单开仓成功，订单号：", ticket, "，价格：", price, "，止损：", sl, trendInfo);

        // 记录止损触发回本信息
        if(UseStopLossRecovery)
        {
            BuyRecoveryInfo.MainOrderTicket = ticket;
            BuyRecoveryInfo.OpenPrice = price;
            BuyRecoveryInfo.OriginalStopLoss = sl;
            BuyRecoveryInfo.OrderType = ORDER_TYPE_BUY;
            BuyRecoveryInfo.LotSize = LotSize;
            BuyRecoveryInfo.IsActive = true;
            BuyRecoveryInfo.IsRecoveryOrderPlaced = false;
        }

        // 记录补仓信息
        if(UseMarginCall)
        {
            BuyMarginInfo.MainOrderTicket = ticket;
            BuyMarginInfo.OpenPrice = price;
            BuyMarginInfo.StopLoss = sl;
            BuyMarginInfo.OrderType = ORDER_TYPE_BUY;
            ResetMarginLevels(BuyMarginInfo);
        }
    }
    else
    {
        Print("多单开仓失败，错误代码：", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| 开空单                                                           |
//+------------------------------------------------------------------+
void OpenSellOrder(double stopLoss)
{
    // 检查是否已有同方向订单
    if(HasOpenPosition(POSITION_TYPE_SELL)) return;

    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = stopLoss;
    double tp = 0; // 不设置止盈

    if(trade.Sell(LotSize, _Symbol, price, sl, tp, "MACD双线策略-空单[" + ChartIdentifier + "]"))
    {
        ulong ticket = trade.ResultOrder();
        string trendInfo = "";
        if(UseMACDTrendFilter)
        {
            trendInfo = "，MACD快线值：" + DoubleToString(DIFBuffer[1], 5);
        }
        Print("空单开仓成功，订单号：", ticket, "，价格：", price, "，止损：", sl, trendInfo);

        // 记录止损触发回本信息
        if(UseStopLossRecovery)
        {
            SellRecoveryInfo.MainOrderTicket = ticket;
            SellRecoveryInfo.OpenPrice = price;
            SellRecoveryInfo.OriginalStopLoss = sl;
            SellRecoveryInfo.OrderType = ORDER_TYPE_SELL;
            SellRecoveryInfo.LotSize = LotSize;
            SellRecoveryInfo.IsActive = true;
            SellRecoveryInfo.IsRecoveryOrderPlaced = false;
        }

        // 记录补仓信息
        if(UseMarginCall)
        {
            SellMarginInfo.MainOrderTicket = ticket;
            SellMarginInfo.OpenPrice = price;
            SellMarginInfo.StopLoss = sl;
            SellMarginInfo.OrderType = ORDER_TYPE_SELL;
            ResetMarginLevels(SellMarginInfo);
        }
    }
    else
    {
        Print("空单开仓失败，错误代码：", trade.ResultRetcode());
    }
}

//+------------------------------------------------------------------+
//| 检查是否有指定类型的开仓订单                                      |
//+------------------------------------------------------------------+
bool HasOpenPosition(ENUM_POSITION_TYPE positionType)
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(positionInfo.SelectByIndex(i))
        {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == UniqueMagicNumber && positionInfo.PositionType() == positionType)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 计算指定类型订单的总手数                                          |
//+------------------------------------------------------------------+
double CalculateTotalLots(ENUM_POSITION_TYPE positionType)
{
    double totalLots = 0;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(positionInfo.SelectByIndex(i))
        {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == UniqueMagicNumber && positionInfo.PositionType() == positionType)
            {
                totalLots += positionInfo.Volume();
            }
        }
    }

    return totalLots;
}

//+------------------------------------------------------------------+
//| 移动止损                                                         |
//+------------------------------------------------------------------+
void TrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(positionInfo.SelectByIndex(i))
        {
            if(positionInfo.Symbol() == _Symbol && positionInfo.Magic() == UniqueMagicNumber)
            {
                if(positionInfo.PositionType() == POSITION_TYPE_BUY)
                {
                    // 多单：检测上一柱最低点是否盈利200点（可调）
                    double baseStopLoss = iLow(_Symbol, _Period, 1) - StopLossBuffer * _Point; // 最低点减去缓冲点数
                    double currentProfit = (baseStopLoss - positionInfo.PriceOpen()) / _Point;

                    // 检查是否达到盈利点数要求且新止损更优
                    if(currentProfit >= TrailingStopPoints && (positionInfo.StopLoss() == 0 || baseStopLoss > positionInfo.StopLoss()))
                    {
                        if(trade.PositionModify(positionInfo.Ticket(), baseStopLoss, positionInfo.TakeProfit()))
                        {
                            Print("多单移动止损成功，订单号：", positionInfo.Ticket(), "，新止损：", baseStopLoss, "，当前盈利：", currentProfit, "点");
                        }
                        else
                        {
                            Print("多单移动止损失败，订单号：", positionInfo.Ticket(), "，错误代码：", trade.ResultRetcode());
                        }
                    }
                }
                else if(positionInfo.PositionType() == POSITION_TYPE_SELL)
                {
                    // 空单：检测上一柱最高点是否盈利200点（可调）
                    double baseStopLoss = iHigh(_Symbol, _Period, 1) + StopLossBuffer * _Point; // 最高点加上缓冲点数
                    double currentProfit = (positionInfo.PriceOpen() - baseStopLoss) / _Point;

                    // 检查是否达到盈利点数要求且新止损更优
                    if(currentProfit >= TrailingStopPoints && (positionInfo.StopLoss() == 0 || baseStopLoss < positionInfo.StopLoss()))
                    {
                        if(trade.PositionModify(positionInfo.Ticket(), baseStopLoss, positionInfo.TakeProfit()))
                        {
                            Print("空单移动止损成功，订单号：", positionInfo.Ticket(), "，新止损：", baseStopLoss, "，当前盈利：", currentProfit, "点");
                        }
                        else
                        {
                            Print("空单移动止损失败，订单号：", positionInfo.Ticket(), "，错误代码：", trade.ResultRetcode());
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化补仓信息                                                    |
//+------------------------------------------------------------------+
void InitMarginCallInfo()
{
    BuyMarginInfo.MainOrderTicket = 0;
    BuyMarginInfo.OpenPrice = 0;
    BuyMarginInfo.StopLoss = 0;
    BuyMarginInfo.OrderType = WRONG_VALUE;

    SellMarginInfo.MainOrderTicket = 0;
    SellMarginInfo.OpenPrice = 0;
    SellMarginInfo.StopLoss = 0;
    SellMarginInfo.OrderType = WRONG_VALUE;

    ResetMarginLevels(BuyMarginInfo);
    ResetMarginLevels(SellMarginInfo);
}

//+------------------------------------------------------------------+
//| 重置补仓级别标记                                                  |
//+------------------------------------------------------------------+
void ResetMarginLevels(MarginCallInfo &info)
{
    for(int i = 0; i < 20; i++)
    {
        info.MarginLevels[i] = false;
        info.MarginOrderTickets[i] = 0;
    }
}

//+------------------------------------------------------------------+
//| 检查补仓条件                                                      |
//+------------------------------------------------------------------+
void CheckMarginCall()
{
    // 检查多单补仓
    if(BuyMarginInfo.MainOrderTicket > 0)
    {
        bool positionExists = false;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(positionInfo.SelectByIndex(i))
            {
                if(positionInfo.Ticket() == BuyMarginInfo.MainOrderTicket)
                {
                    positionExists = true;
                    break;
                }
            }
        }

        if(positionExists)
        {
            CheckBuyMarginCall();
        }
        else
        {
            // 主订单已关闭，重置补仓信息
            ResetMarginCallInfo(BuyMarginInfo);
        }
    }

    // 检查空单补仓
    if(SellMarginInfo.MainOrderTicket > 0)
    {
        bool positionExists = false;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(positionInfo.SelectByIndex(i))
            {
                if(positionInfo.Ticket() == SellMarginInfo.MainOrderTicket)
                {
                    positionExists = true;
                    break;
                }
            }
        }

        if(positionExists)
        {
            CheckSellMarginCall();
        }
        else
        {
            // 主订单已关闭，重置补仓信息
            ResetMarginCallInfo(SellMarginInfo);
        }
    }
}

//+------------------------------------------------------------------+
//| 重置补仓信息                                                      |
//+------------------------------------------------------------------+
void ResetMarginCallInfo(MarginCallInfo &info)
{
    info.MainOrderTicket = 0;
    info.OpenPrice = 0;
    info.StopLoss = 0;
    info.OrderType = WRONG_VALUE;
    ResetMarginLevels(info);
}

//+------------------------------------------------------------------+
//| 检查多单补仓                                                      |
//+------------------------------------------------------------------+
void CheckBuyMarginCall()
{
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double openPrice = BuyMarginInfo.OpenPrice;
    double stopLoss = BuyMarginInfo.StopLoss;

    // 计算开仓价和止损价之间的距离
    double totalDistance = openPrice - stopLoss;
    if(totalDistance <= 0) return; // 无效距离

    // 计算每一份的距离
    double stepDistance = totalDistance / (MaxMarginOrders + 1);

    // 检查每个补仓级别
    for(int level = 1; level <= MaxMarginOrders && level <= 20; level++) // 添加数组边界检查
    {
        if(!BuyMarginInfo.MarginLevels[level - 1]) // 该级别还未补仓
        {
            double marginPrice = openPrice - stepDistance * level;

            // 检查当前价格是否到达补仓点
            if(currentPrice <= marginPrice)
            {
                // 执行补仓
                double askPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                if(trade.Buy(LotSize, _Symbol, askPrice, stopLoss, 0, "补仓-" + IntegerToString(level) + "[" + ChartIdentifier + "]"))
                {
                    ulong ticket = trade.ResultOrder();
                    BuyMarginInfo.MarginLevels[level - 1] = true;
                    BuyMarginInfo.MarginOrderTickets[level - 1] = ticket;
                    Print("多单补仓成功，级别：", level, "，订单号：", ticket, "，价格：", askPrice);
                }
                else
                {
                    Print("多单补仓失败，级别：", level, "，错误代码：", trade.ResultRetcode());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查空单补仓                                                      |
//+------------------------------------------------------------------+
void CheckSellMarginCall()
{
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double openPrice = SellMarginInfo.OpenPrice;
    double stopLoss = SellMarginInfo.StopLoss;

    // 计算开仓价和止损价之间的距离
    double totalDistance = stopLoss - openPrice;
    if(totalDistance <= 0) return; // 无效距离

    // 计算每一份的距离
    double stepDistance = totalDistance / (MaxMarginOrders + 1);

    // 检查每个补仓级别
    for(int level = 1; level <= MaxMarginOrders && level <= 20; level++) // 添加数组边界检查
    {
        if(!SellMarginInfo.MarginLevels[level - 1]) // 该级别还未补仓
        {
            double marginPrice = openPrice + stepDistance * level;

            // 检查当前价格是否到达补仓点
            if(currentPrice >= marginPrice)
            {
                // 执行补仓
                double bidPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                if(trade.Sell(LotSize, _Symbol, bidPrice, stopLoss, 0, "补仓-" + IntegerToString(level) + "[" + ChartIdentifier + "]"))
                {
                    ulong ticket = trade.ResultOrder();
                    SellMarginInfo.MarginLevels[level - 1] = true;
                    SellMarginInfo.MarginOrderTickets[level - 1] = ticket;
                    Print("空单补仓成功，级别：", level, "，订单号：", ticket, "，价格：", bidPrice);
                }
                else
                {
                    Print("空单补仓失败，级别：", level, "，错误代码：", trade.ResultRetcode());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化止损触发回本信息                                            |
//+------------------------------------------------------------------+
void InitStopLossRecoveryInfo()
{
    BuyRecoveryInfo.MainOrderTicket = 0;
    BuyRecoveryInfo.OpenPrice = 0;
    BuyRecoveryInfo.OriginalStopLoss = 0;
    BuyRecoveryInfo.OrderType = WRONG_VALUE;
    BuyRecoveryInfo.LotSize = 0;
    BuyRecoveryInfo.IsActive = false;
    BuyRecoveryInfo.IsRecoveryOrderPlaced = false;

    SellRecoveryInfo.MainOrderTicket = 0;
    SellRecoveryInfo.OpenPrice = 0;
    SellRecoveryInfo.OriginalStopLoss = 0;
    SellRecoveryInfo.OrderType = WRONG_VALUE;
    SellRecoveryInfo.LotSize = 0;
    SellRecoveryInfo.IsActive = false;
    SellRecoveryInfo.IsRecoveryOrderPlaced = false;
}

//+------------------------------------------------------------------+
//| 检查止损是否触发                                                  |
//+------------------------------------------------------------------+
void CheckStopLossTriggered()
{
    // 检查多单止损触发
    if(BuyRecoveryInfo.IsActive && !BuyRecoveryInfo.IsRecoveryOrderPlaced)
    {
        // 检查主订单是否还存在
        bool positionExists = false;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(positionInfo.SelectByIndex(i))
            {
                if(positionInfo.Ticket() == BuyRecoveryInfo.MainOrderTicket)
                {
                    positionExists = true;
                    break;
                }
            }
        }

        if(!positionExists) // 订单已关闭
        {
            // 检查是否是因为止损触发关闭的
            double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            if(currentPrice <= BuyRecoveryInfo.OriginalStopLoss + 5 * _Point) // 允许5点滑点
            {
                // 止损触发，开设反向空单
                OpenRecoveryOrder(ORDER_TYPE_SELL, BuyRecoveryInfo.LotSize, BuyRecoveryInfo.OpenPrice, BuyRecoveryInfo.OriginalStopLoss);
                BuyRecoveryInfo.IsRecoveryOrderPlaced = true;
            }
            else
            {
                // 不是止损触发的关闭，重置监控
                BuyRecoveryInfo.IsActive = false;
            }
        }
    }

    // 检查空单止损触发
    if(SellRecoveryInfo.IsActive && !SellRecoveryInfo.IsRecoveryOrderPlaced)
    {
        // 检查主订单是否还存在
        bool positionExists = false;
        for(int i = 0; i < PositionsTotal(); i++)
        {
            if(positionInfo.SelectByIndex(i))
            {
                if(positionInfo.Ticket() == SellRecoveryInfo.MainOrderTicket)
                {
                    positionExists = true;
                    break;
                }
            }
        }

        if(!positionExists) // 订单已关闭
        {
            // 检查是否是因为止损触发关闭的
            double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            if(currentPrice >= SellRecoveryInfo.OriginalStopLoss - 5 * _Point) // 允许5点滑点
            {
                // 止损触发，开设反向多单
                OpenRecoveryOrder(ORDER_TYPE_BUY, SellRecoveryInfo.LotSize, SellRecoveryInfo.OpenPrice, SellRecoveryInfo.OriginalStopLoss);
                SellRecoveryInfo.IsRecoveryOrderPlaced = true;
            }
            else
            {
                // 不是止损触发的关闭，重置监控
                SellRecoveryInfo.IsActive = false;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 开设反向回本单                                                    |
//+------------------------------------------------------------------+
void OpenRecoveryOrder(ENUM_ORDER_TYPE orderType, double lotSize, double originalPrice, double stopLossPrice)
{
    double price, sl, tp;
    string orderComment;

    // 计算亏损点数（注意：这里不使用Point进行除法，直接计算价格差）
    double lossPoints;

    if(orderType == ORDER_TYPE_BUY)
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        // 计算前三柱最低点作为止损，并减去缓冲点数
        sl = GetLowestPrice(1, RecoveryLookback) - RecoveryStopLossBuffer * _Point;

        // 计算亏损点数（空单触发止损的亏损）
        lossPoints = MathAbs(stopLossPrice - originalPrice);

        // 设置止盈：当前价格 + 首单亏损点数
        if(UseRecoveryTakeProfit)
        {
            tp = price + lossPoints;
        }
        else
        {
            tp = 0;
        }

        orderComment = "止损回本-多单";
    }
    else // ORDER_TYPE_SELL
    {
        price = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        // 计算前三柱最高点作为止损，并加上缓冲点数
        sl = GetHighestPrice(1, RecoveryLookback) + RecoveryStopLossBuffer * _Point;

        // 计算亏损点数（多单触发止损的亏损）
        lossPoints = MathAbs(originalPrice - stopLossPrice);

        // 设置止盈：当前价格 - 首单亏损点数
        if(UseRecoveryTakeProfit)
        {
            tp = price - lossPoints;
        }
        else
        {
            tp = 0;
        }

        orderComment = "止损回本-空单";
    }

    bool result = false;
    ulong ticket = 0;

    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(lotSize, _Symbol, price, sl, tp, orderComment + "[" + ChartIdentifier + "]");
    }
    else
    {
        result = trade.Sell(lotSize, _Symbol, price, sl, tp, orderComment + "[" + ChartIdentifier + "]");
    }

    if(result)
    {
        ticket = trade.ResultOrder();
        Print("止损触发回本单开仓成功，订单号：", ticket,
             "，类型：", orderType == ORDER_TYPE_BUY ? "多单" : "空单",
             "，价格：", price,
             "，止损：", sl,
             "，止盈：", tp != 0 ? DoubleToString(tp, _Digits) : "无",
             "，手数：", lotSize,
             "，首单亏损点数：", DoubleToString(lossPoints, _Digits));
    }
    else
    {
        Print("止损触发回本单开仓失败，错误代码：", trade.ResultRetcode());
    }
}
